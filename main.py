#!/usr/bin/env python3
"""
Simple Zork LLM Player - LLM controlled version with reliable I/O
"""

import subprocess
import time
import sys
import threading
import queue
import requests

class ZorkLLMPlayer:
    # Colors
    YELLOW = '\033[93m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    RED = '\033[91m'
    RESET = '\033[0m'

    # LLM Configuration
    OLLAMA_URL = "http://localhost:11434/api/generate"
    MODEL = "qwen3:latest"
    MAX_TURNS = 100

    def __init__(self):
        self.game_process = None
        self.output_queue = queue.Queue()
        self.reader_thread = None
        self.turn_count = 0
        self.conversation_history = []
        
    def start_zork(self):
        """Start the Zork game process"""
        try:
            self.game_process = subprocess.Popen(
                ["zork"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Combine stderr with stdout
                universal_newlines=True,
                bufsize=0  # Unbuffered
            )
            print(f"{self.GREEN}✓ Zork started{self.RESET}")
            
            # Start background thread to read output
            self.reader_thread = threading.Thread(target=self._read_output_continuously, daemon=True)
            self.reader_thread.start()
            
            return True
        except FileNotFoundError:
            print(f"{self.RED}✗ Zork not found. Install with: brew install zork{self.RESET}")
            return False
        except Exception as e:
            print(f"{self.RED}✗ Error starting Zork: {e}{self.RESET}")
            return False
    
    def _read_output_continuously(self):
        """Background thread to continuously read game output"""
        while self.game_process and self.game_process.poll() is None:
            try:
                line = self.game_process.stdout.readline()
                if line:
                    self.output_queue.put(line.rstrip('\n\r'))
                else:
                    break
            except Exception as e:
                print(f"Reader thread error: {e}")
                break
    
    def get_game_output(self, timeout=5):
        """Get accumulated game output"""
        lines = []
        start_time = time.time()
        
        # Collect all available output
        while time.time() - start_time < timeout:
            try:
                line = self.output_queue.get(timeout=0.1)
                lines.append(line)
                
                # Check if we got the prompt
                if line.strip() == '>' or line.strip().endswith('>'):
                    break
            except queue.Empty:
                # No more output available
                if lines:  # If we have some output, we might be done
                    break
                continue
        
        return lines
    
    def display_output(self, lines):
        """Display game output to user"""
        for line in lines:
            line_clean = line.strip()
            if line_clean and line_clean != '>':
                # Remove trailing '>' if present
                if line_clean.endswith('>'):
                    content = line_clean[:-1].strip()
                    if content:
                        print(f"{self.YELLOW}{content}{self.RESET}")
                else:
                    print(f"{self.YELLOW}{line_clean}{self.RESET}")
    
    def build_context(self):
        """Build conversation context from recent history"""
        if not self.conversation_history:
            return ""

        # Show last 8 interactions for better context
        recent = self.conversation_history[-8:]
        context_lines = []
        for cmd, response in recent:
            context_lines.append(f"You tried: {cmd}")
            context_lines.append(f"Game said: {response}")

        return "\n".join(context_lines)

    def query_llm(self, game_output):
        """Query LLM for next command with context and better guidance"""
        context = self.build_context()

        prompt = f"""You are playing Zork, a classic text adventure game. Your goal is to explore, solve puzzles, find treasure, and complete the adventure.

CRITICAL GAME MECHANICS:
1. OBJECTS IN DESCRIPTIONS CAN BE INTERACTED WITH:
   - If you see "A leaflet", "A lamp", "A sword" etc. → use "take [item]"
   - If you see "mailbox", "door", "chest", "case" → use "open [container]"
   - If you see "window", "painting", "inscription" → use "examine [object]"
   - If you see "book", "leaflet", "sign", "note" → use "read [item]" (after taking it)

2. INVENTORY MANAGEMENT:
   - Use "inventory" or "i" regularly to see what you're carrying
   - Items you take are now in your possession and can be used
   - You can "drop [item]" if needed, but usually keep useful items

3. COMPREHENSIVE ACTION OPTIONS:
   - MOVEMENT: north, south, east, west, up, down, enter, exit
   - INTERACTION: take, drop, open, close, examine, read, use
   - INFORMATION: look, inventory, score
   - PROBLEM SOLVING: push, pull, turn, move, climb, jump

4. LOGICAL PROGRESSION:
   - Open containers → take items → examine/read items → use items
   - If stuck in a location, try ALL directions and examine ALL objects
   - Items often provide clues or are needed for puzzles

IMPORTANT RULES:
- If a command fails, DON'T repeat it - try a different approach
- ALWAYS take items you find - they're usually important
- Read items after taking them to get information
- Use "inventory" to track what you have
- Examine objects before trying to use them

Recent actions and their results:
{context}

Current game state:
{game_output}

ANALYSIS CHECKLIST:
1. Are there any objects mentioned that I haven't taken? → take them
2. Are there any containers I haven't opened? → open them
3. Have I checked my inventory recently? → use "inventory"
4. Are there items I've taken but not read/examined? → read/examine them
5. Have I tried all directions from this location? → try unexplored directions
6. Are there objects I can examine for more information? → examine them

Think through the situation systematically, then provide your final command.

After your reasoning, end your response with:
FINAL_COMMAND: [your command here]

Examples:
FINAL_COMMAND: take leaflet
FINAL_COMMAND: inventory
FINAL_COMMAND: examine window
FINAL_COMMAND: open door
FINAL_COMMAND: read leaflet"""

        try:
            payload = {
                "model": self.MODEL,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.8,  # Slightly higher for more creativity
                    "num_predict": 500   # High enough for complete thinking + command
                }
            }

            response = requests.post(self.OLLAMA_URL, json=payload, timeout=120)
            response.raise_for_status()

            result = response.json()
            llm_response = result.get('response', '').strip()

            print(f"🔍 Response length: {len(llm_response)} chars")
            print(f"🔍 Response preview: {repr(llm_response[:100])}...")
            print(f"🔍 Response ending: {repr(llm_response[-100:])}")  # Show the end too

            # Extract command from thinking response
            command = self.extract_command_from_thinking(llm_response)

            return command

        except Exception as e:
            print(f"{self.RED}✗ LLM Error: {e}{self.RESET}")
            return "look"  # Fallback

    def extract_command_from_thinking(self, response):
        """Extract final command from thinking model response"""
        if not response:
            return "look"

        import re

        # Strategy 1: Look for FINAL_COMMAND: marker
        final_command_match = re.search(r'FINAL_COMMAND:\s*([^\n]+)', response, re.IGNORECASE)
        if final_command_match:
            command = final_command_match.group(1).strip()
            words = command.lower().split()[:2]
            return " ".join(words) if words else "look"

        # Strategy 2: Look for content after </think> tag
        think_end_match = re.search(r'</think>\s*(.+)', response, re.IGNORECASE | re.DOTALL)
        if think_end_match:
            after_think = think_end_match.group(1).strip()
            # Take first meaningful line after thinking
            lines = after_think.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith(('I', 'The', 'Based', 'Looking', 'Given', 'So')):
                    words = line.lower().split()[:2]
                    if words and len(words[0]) > 1:
                        return " ".join(words)

        # Strategy 3: Look for commands in the last part of the response
        # Split by sentences and look for command-like patterns
        sentences = re.split(r'[.!?]\s+', response)
        for sentence in reversed(sentences[-3:]):  # Check last 3 sentences
            sentence = sentence.strip()
            if sentence:
                # Look for patterns like "I should X" or "Let me X" or just "X"
                command_patterns = [
                    r'(?:i should|let me|i\'ll|i will)\s+([a-z\s]+)',
                    r'(?:try|do|use)\s+([a-z\s]+)',
                    r'^([a-z]+(?:\s+[a-z]+)?)$'  # Simple command at start of sentence
                ]

                for pattern in command_patterns:
                    match = re.search(pattern, sentence.lower())
                    if match:
                        command = match.group(1).strip()
                        words = command.split()[:2]
                        if words and words[0] in ['north', 'south', 'east', 'west', 'up', 'down', 'look', 'examine', 'take', 'open', 'close', 'read', 'inventory', 'drop', 'go', 'enter']:
                            return " ".join(words)

        # Strategy 4: Remove thinking tags and extract from remaining text
        clean_response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL | re.IGNORECASE)
        clean_response = clean_response.strip()

        if clean_response:
            lines = clean_response.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith(('I', 'The', 'Based', 'Looking', 'Given')):
                    words = line.lower().split()[:2]
                    if words and len(words[0]) > 1:
                        return " ".join(words)

        # Strategy 5: Look for common Zork actions in the response
        game_commands = [
            'open mailbox', 'take leaflet', 'read leaflet', 'inventory', 'examine mailbox',
            'north', 'south', 'east', 'west', 'up', 'down', 'look', 'examine', 'take',
            'open', 'close', 'read', 'drop', 'go', 'enter'
        ]
        response_lower = response.lower()
        for cmd in game_commands:
            if cmd in response_lower:
                return cmd

        # Strategy 6: If response seems incomplete (ends with thinking), return a safe command
        if response.rstrip().endswith(('<think', '<think>')):
            return "open mailbox"  # Good starting command

        return "look"  # Ultimate fallback

    def send_command(self, command):
        """Send command to the game"""
        try:
            self.game_process.stdin.write(command + "\n")
            self.game_process.stdin.flush()
            return True
        except Exception as e:
            print(f"{self.RED}✗ Error sending command: {e}{self.RESET}")
            return False
    
    def play_game(self):
        """Main game loop - LLM controlled"""
        if not self.start_zork():
            return False

        print(f"🎮 Starting LLM-Controlled Zork\n")

        # Wait for initial output
        time.sleep(1)
        initial_output = self.get_game_output(timeout=10)
        self.display_output(initial_output)

        # Convert output lines to text for LLM
        game_text = "\n".join(initial_output)

        try:
            while self.turn_count < self.MAX_TURNS:
                self.turn_count += 1
                print(f"\n--- Turn {self.turn_count} ---")

                # Get command from LLM
                print(f"🤖 Querying LLM...")
                command = self.query_llm(game_text)
                print(f"🎯 {self.CYAN}LLM: '{command}'{self.RESET}")

                # Send command to game
                if not self.send_command(command):
                    break

                # Get and display response
                time.sleep(0.2)  # Brief pause for game to respond
                output_lines = self.get_game_output()
                self.display_output(output_lines)

                # Update game text and conversation history
                game_text = "\n".join(output_lines)
                self.conversation_history.append((command, game_text))

                # Keep history manageable but longer for better context
                if len(self.conversation_history) > 15:
                    self.conversation_history = self.conversation_history[-12:]

                time.sleep(1)  # Pause between turns

        except KeyboardInterrupt:
            print(f"\n{self.GREEN}Game interrupted by user{self.RESET}")
        except Exception as e:
            print(f"{self.RED}Error in game loop: {e}{self.RESET}")
        finally:
            self.cleanup()

        print(f"\n🎯 Game completed after {self.turn_count} turns")
        return True
    
    def cleanup(self):
        """Clean up game process"""
        if self.game_process:
            try:
                self.game_process.terminate()
                self.game_process.wait(timeout=5)
            except:
                self.game_process.kill()
            print(f"{self.GREEN}✓ Game process cleaned up{self.RESET}")

def main():
    print("Simple Zork LLM Player")
    print("=" * 30)

    # Check Ollama
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print(f"{ZorkLLMPlayer.GREEN}✓ Ollama is running{ZorkLLMPlayer.RESET}")
        else:
            print(f"{ZorkLLMPlayer.RED}✗ Ollama not responding{ZorkLLMPlayer.RESET}")
            sys.exit(1)
    except:
        print(f"{ZorkLLMPlayer.RED}✗ Cannot connect to Ollama{ZorkLLMPlayer.RESET}")
        sys.exit(1)

    player = ZorkLLMPlayer()
    player.play_game()

if __name__ == "__main__":
    main()

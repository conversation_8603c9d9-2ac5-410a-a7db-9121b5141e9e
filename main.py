#!/usr/bin/env python3
"""
Simple Zork Player - Human controlled version to test I/O
"""

import subprocess
import time
import sys
import threading
import queue

class ZorkPlayer:
    # Colors
    YELLOW = '\033[93m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    RED = '\033[91m'
    RESET = '\033[0m'
    
    def __init__(self):
        self.game_process = None
        self.output_queue = queue.Queue()
        self.reader_thread = None
        
    def start_zork(self):
        """Start the Zork game process"""
        try:
            self.game_process = subprocess.Popen(
                ["zork"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Combine stderr with stdout
                universal_newlines=True,
                bufsize=0  # Unbuffered
            )
            print(f"{self.GREEN}✓ Zork started{self.RESET}")
            
            # Start background thread to read output
            self.reader_thread = threading.Thread(target=self._read_output_continuously, daemon=True)
            self.reader_thread.start()
            
            return True
        except FileNotFoundError:
            print(f"{self.RED}✗ Zork not found. Install with: brew install zork{self.RESET}")
            return False
        except Exception as e:
            print(f"{self.RED}✗ Error starting Zork: {e}{self.RESET}")
            return False
    
    def _read_output_continuously(self):
        """Background thread to continuously read game output"""
        while self.game_process and self.game_process.poll() is None:
            try:
                line = self.game_process.stdout.readline()
                if line:
                    self.output_queue.put(line.rstrip('\n\r'))
                else:
                    break
            except Exception as e:
                print(f"Reader thread error: {e}")
                break
    
    def get_game_output(self, timeout=5):
        """Get accumulated game output"""
        lines = []
        start_time = time.time()
        
        # Collect all available output
        while time.time() - start_time < timeout:
            try:
                line = self.output_queue.get(timeout=0.1)
                lines.append(line)
                
                # Check if we got the prompt
                if line.strip() == '>' or line.strip().endswith('>'):
                    break
            except queue.Empty:
                # No more output available
                if lines:  # If we have some output, we might be done
                    break
                continue
        
        return lines
    
    def display_output(self, lines):
        """Display game output to user"""
        for line in lines:
            line_clean = line.strip()
            if line_clean and line_clean != '>':
                # Remove trailing '>' if present
                if line_clean.endswith('>'):
                    content = line_clean[:-1].strip()
                    if content:
                        print(f"{self.YELLOW}{content}{self.RESET}")
                else:
                    print(f"{self.YELLOW}{line_clean}{self.RESET}")
    
    def send_command(self, command):
        """Send command to the game"""
        try:
            self.game_process.stdin.write(command + "\n")
            self.game_process.stdin.flush()
            return True
        except Exception as e:
            print(f"{self.RED}✗ Error sending command: {e}{self.RESET}")
            return False
    
    def play_game(self):
        """Main game loop - human controlled"""
        if not self.start_zork():
            return False
        
        print(f"🎮 Starting Human-Controlled Zork\n")
        print("Type 'quit' to exit\n")
        
        # Wait for initial output
        time.sleep(1)
        initial_output = self.get_game_output(timeout=10)
        self.display_output(initial_output)
        
        try:
            while True:
                # Get user input
                try:
                    user_command = input(f"\n{self.CYAN}> {self.RESET}").strip()
                except (EOFError, KeyboardInterrupt):
                    print(f"\n{self.GREEN}Goodbye!{self.RESET}")
                    break
                
                if user_command.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not user_command:
                    continue
                
                # Send command to game
                if not self.send_command(user_command):
                    break
                
                # Get and display response
                time.sleep(0.2)  # Brief pause for game to respond
                output_lines = self.get_game_output()
                self.display_output(output_lines)
                
        except Exception as e:
            print(f"{self.RED}Error in game loop: {e}{self.RESET}")
        finally:
            self.cleanup()
        
        return True
    
    def cleanup(self):
        """Clean up game process"""
        if self.game_process:
            try:
                self.game_process.terminate()
                self.game_process.wait(timeout=5)
            except:
                self.game_process.kill()
            print(f"{self.GREEN}✓ Game process cleaned up{self.RESET}")

def main():
    print("Simple Human-Controlled Zork Player")
    print("=" * 40)
    
    player = ZorkPlayer()
    player.play_game()

if __name__ == "__main__":
    main()

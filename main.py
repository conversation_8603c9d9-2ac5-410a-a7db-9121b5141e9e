#!/usr/bin/env python3
"""
Simple Zork LLM Player - Clean implementation following the specified flow
"""

import subprocess
import time
import requests
import sys
import re

class ZorkLLMPlayer:
    # Colors
    YELLOW = '\033[93m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    RED = '\033[91m'
    RESET = '\033[0m'
    
    # Configuration
    OLLAMA_URL = "http://localhost:11434/api/generate"
    MODEL = "deepseek-r1"
    MAX_TURNS = 100
    
    # Valid Zork commands from https://zork.fandom.com/wiki/Command_List
    VALID_COMMANDS = {
        # Movement
        'north', 'n', 'south', 's', 'east', 'e', 'west', 'w',
        'northeast', 'ne', 'northwest', 'nw', 'southeast', 'se', 'southwest', 'sw',
        'up', 'u', 'down', 'd', 'go', 'enter', 'in', 'out', 'climb',
        
        # Items
        'get', 'take', 'grab', 'drop', 'put', 'throw', 'open', 'close', 'read',
        'turn', 'move', 'attack', 'examine', 'inventory', 'i', 'eat', 'tie', 'pick',
        'break', 'kill', 'drink', 'smell', 'cut', 'listen',
        
        # Other
        'look', 'l', 'save', 'restore', 'restart', 'verbose', 'score', 'diagnose',
        'brief', 'superbrief', 'quit', 'q', 'g', 'hi', 'hello', 'shout', 'yell',
        'scream', 'pray', 'bar', 'jump', 'swing'
    }
    
    def __init__(self):
        self.game_process = None
        self.turn_count = 0
        
    def start_zork(self):
        """Start the Zork game process"""
        try:
            self.game_process = subprocess.Popen(
                ["zork"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=0  # Unbuffered
            )
            print(f"{self.GREEN}✓ Zork started{self.RESET}")
            return True
        except FileNotFoundError:
            print(f"{self.RED}✗ Zork not found. Install with: brew install zork{self.RESET}")
            return False
        except Exception as e:
            print(f"{self.RED}✗ Error starting Zork: {e}{self.RESET}")
            return False
    
    def read_game_output(self, timeout=10):
        """Read output from the game until we see the '>' prompt"""
        output = ""
        display_buffer = ""
        start_time = time.time()
        prompt_found = False

        while time.time() - start_time < timeout and not prompt_found:
            if self.game_process.poll() is not None:
                break

            try:
                # Check if data is available
                import select
                ready, _, _ = select.select([self.game_process.stdout], [], [], 0.1)
                if ready:
                    # Read one character at a time to handle prompt properly
                    char = self.game_process.stdout.read(1)
                    if char:
                        if char == '>':
                            # Found the prompt - flush any remaining display buffer
                            if display_buffer.strip():
                                print(f"{self.YELLOW}{display_buffer.rstrip()}{self.RESET}")
                                output += display_buffer
                            prompt_found = True
                            break
                        elif char == '\n':
                            # End of line - display what we have and add to output
                            print(f"{self.YELLOW}{display_buffer.rstrip()}{self.RESET}")
                            output += display_buffer + char
                            display_buffer = ""
                        else:
                            # Regular character - add to buffer
                            display_buffer += char
                    else:
                        # No more data available right now
                        time.sleep(0.1)
                else:
                    # No data available, wait a bit
                    time.sleep(0.1)
            except Exception as e:
                print(f"Error reading: {e}")
                break

        # Handle any remaining buffer if we didn't find prompt
        if not prompt_found:
            if display_buffer.strip():
                print(f"{self.YELLOW}{display_buffer.rstrip()}{self.RESET}")
                output += display_buffer
            if not output:
                print(f"{self.RED}⚠ Warning: No output received within {timeout}s{self.RESET}")
            else:
                print(f"{self.YELLOW}⚠ Note: Complete output received but no '>' prompt found{self.RESET}")

        return output.strip()
    
    def query_llm(self, game_output, context=""):
        """Send game output to LLM and get command response"""

        system_prompt = f"""You are playing a classic text adventure game like Zork. Your objective is to explore, solve puzzles, and complete the adventure by issuing valid game commands.

Rules:
1. Respond ONLY with a SINGLE game command each turn. Do NOT include explanations, reasoning, or multiple commands.
2. Commands should be concise, typically one or two words, using standard adventure verbs (e.g., north, take lamp, open door, examine object).
3. Always consider the game’s last output carefully to understand your surroundings and what actions are possible.
4. Avoid repeating identical commands that have already succeeded or failed; instead, try new logical actions or explore new areas.
5. If unsure, prioritize commands that gather information, such as "look", "examine [object]", or "inventory".
6. Use directions (north, south, east, west, up, down) to navigate through the environment.
7. When stuck, try interacting with objects in the environment or using items in your inventory.
8. Avoid commands unrelated to progressing the game or gathering useful information.
9. Remember the goal: exploration, puzzle solving, and advancing the story.

Current game state:
{game_output}

{context}

What command do you want to execute?"""

        try:
            payload = {
                "model": self.MODEL,
                "prompt": system_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "num_predict": 50,
                    "stop": ["\n\n", "Command:", ">"],
                    "enable_thinking": False  # Disable reasoning for DeepSeek-R1
                }
            }

            print(f"🤖 Querying LLM...")
            print(f"📝 Sending prompt: {system_prompt[-100:]}...")  # Debug: show end of prompt

            response = requests.post(self.OLLAMA_URL, json=payload, timeout=30)
            response.raise_for_status()

            result = response.json()
            llm_response = result.get('response', '').strip()

            print(f"📥 Raw LLM response: '{llm_response}'")  # Debug: show raw response

            # Extract clean command
            command = self.extract_command(llm_response)
            print(f"🎯 {self.CYAN}LLM: '{command}'{self.RESET}")

            return command

        except Exception as e:
            print(f"{self.RED}✗ LLM Error: {e}{self.RESET}")
            return "look"  # Fallback
    
    def extract_command(self, response):
        """Extract a clean command from LLM response"""
        if not response:
            return "look"

        print(f"🔍 Debug: Raw response: '{response[:100]}...'")  # Debug info

        # Remove reasoning tokens (handle incomplete ones too)
        response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL | re.IGNORECASE)
        response = re.sub(r'<think>.*', '', response, flags=re.DOTALL | re.IGNORECASE)  # Handle incomplete

        # Clean up
        response = response.strip()
        response = response.strip('"\'')

        # If response is empty after removing think tags, try some common commands
        if not response.strip():
            print("🔍 Debug: Empty response after cleaning, trying 'examine mailbox'")
            return "examine mailbox"

        # Take first line if multiple lines
        first_line = response.split('\n')[0].strip()

        # Take first 1-2 words
        words = first_line.lower().split()[:2]
        if not words:
            return "examine mailbox"  # Better fallback than "look"

        command = " ".join(words)

        print(f"🔍 Debug: Extracted '{command}' from '{first_line}'")  # Debug info

        # Validate against known commands (more lenient)
        if words[0] in self.VALID_COMMANDS:
            return command

        # If not in valid commands, still try it (maybe it's a valid combination)
        if len(words) <= 2:
            return command

        return "examine mailbox"  # Better fallback
    
    def send_command(self, command):
        """Send command to the game"""
        try:
            self.game_process.stdin.write(command + "\n")
            self.game_process.stdin.flush()
            return True
        except Exception as e:
            print(f"{self.RED}✗ Error sending command: {e}{self.RESET}")
            return False
    
    def play_game(self):
        """Main game loop following the specified flow"""
        if not self.start_zork():
            return False
        
        print(f"🎮 Starting Zork LLM Player\n")
        
        # Read initial game output (longer timeout for intro text)
        game_output = self.read_game_output(timeout=15)
        context = ""
        
        try:
            while self.turn_count < self.MAX_TURNS:
                self.turn_count += 1
                print(f"\n--- Turn {self.turn_count} ---")
                
                # Send game output to LLM and get command
                command = self.query_llm(game_output, context)
                
                # Send command to game
                if not self.send_command(command):
                    break
                
                # Read game response
                game_output = self.read_game_output()
                
                # Build context for next turn (keep last few turns)
                if len(context.split('\n')) > 10:  # Limit context size
                    context = '\n'.join(context.split('\n')[-8:])
                context += f"\nPrevious: {command} -> {game_output[:100]}..."
                
                time.sleep(0.5)  # Brief pause
                
        except KeyboardInterrupt:
            print(f"\n{self.GREEN}Game interrupted by user{self.RESET}")
        finally:
            self.cleanup()
        
        print(f"\n🎯 Game completed after {self.turn_count} turns")
        return True
    
    def cleanup(self):
        """Clean up game process"""
        if self.game_process:
            try:
                self.game_process.terminate()
                self.game_process.wait(timeout=5)
            except:
                self.game_process.kill()
            print(f"{self.GREEN}✓ Game process cleaned up{self.RESET}")

def main():
    print("Simple Zork LLM Player")
    print("=" * 30)
    
    # Check Ollama
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print(f"{ZorkLLMPlayer.GREEN}✓ Ollama is running{ZorkLLMPlayer.RESET}")
        else:
            print(f"{ZorkLLMPlayer.RED}✗ Ollama not responding{ZorkLLMPlayer.RESET}")
            sys.exit(1)
    except:
        print(f"{ZorkLLMPlayer.RED}✗ Cannot connect to Ollama{ZorkLLMPlayer.RESET}")
        sys.exit(1)
    
    player = ZorkLLMPlayer()
    player.play_game()

if __name__ == "__main__":
    main()

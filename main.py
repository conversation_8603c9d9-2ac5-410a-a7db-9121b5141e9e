#!/usr/bin/env python3
"""
Simple Zork LLM Player - LLM controlled version with reliable I/O
"""

import subprocess
import time
import sys
import threading
import queue
import requests

class ZorkLLMPlayer:
    # Colors
    YELLOW = '\033[93m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    RED = '\033[91m'
    RESET = '\033[0m'

    # LLM Configuration
    OLLAMA_URL = "http://localhost:11434/api/generate"
    MODEL = "qwen3:latest"
    MAX_TURNS = 100

    def __init__(self):
        self.game_process = None
        self.output_queue = queue.Queue()
        self.reader_thread = None
        self.turn_count = 0
        self.conversation_history = []
        
    def start_zork(self):
        """Start the Zork game process"""
        try:
            self.game_process = subprocess.Popen(
                ["zork"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Combine stderr with stdout
                universal_newlines=True,
                bufsize=0  # Unbuffered
            )
            print(f"{self.GREEN}✓ Zork started{self.RESET}")
            
            # Start background thread to read output
            self.reader_thread = threading.Thread(target=self._read_output_continuously, daemon=True)
            self.reader_thread.start()
            
            return True
        except FileNotFoundError:
            print(f"{self.RED}✗ Zork not found. Install with: brew install zork{self.RESET}")
            return False
        except Exception as e:
            print(f"{self.RED}✗ Error starting Zork: {e}{self.RESET}")
            return False
    
    def _read_output_continuously(self):
        """Background thread to continuously read game output"""
        while self.game_process and self.game_process.poll() is None:
            try:
                line = self.game_process.stdout.readline()
                if line:
                    self.output_queue.put(line.rstrip('\n\r'))
                else:
                    break
            except Exception as e:
                print(f"Reader thread error: {e}")
                break
    
    def get_game_output(self, timeout=5):
        """Get accumulated game output"""
        lines = []
        start_time = time.time()
        
        # Collect all available output
        while time.time() - start_time < timeout:
            try:
                line = self.output_queue.get(timeout=0.1)
                lines.append(line)
                
                # Check if we got the prompt
                if line.strip() == '>' or line.strip().endswith('>'):
                    break
            except queue.Empty:
                # No more output available
                if lines:  # If we have some output, we might be done
                    break
                continue
        
        return lines
    
    def display_output(self, lines):
        """Display game output to user"""
        for line in lines:
            line_clean = line.strip()
            if line_clean and line_clean != '>':
                # Remove trailing '>' if present
                if line_clean.endswith('>'):
                    content = line_clean[:-1].strip()
                    if content:
                        print(f"{self.YELLOW}{content}{self.RESET}")
                else:
                    print(f"{self.YELLOW}{line_clean}{self.RESET}")
    
    def build_context(self):
        """Build conversation context from recent history"""
        if not self.conversation_history:
            return ""

        # Show last 5 interactions
        recent = self.conversation_history[-5:]
        context_lines = []
        for cmd, response in recent:
            context_lines.append(f"You tried: {cmd}")
            context_lines.append(f"Game said: {response}")

        return "\n".join(context_lines)

    def query_llm(self, game_output):
        """Query LLM for next command with context and better guidance"""
        context = self.build_context()

        prompt = f"""/no_think

You are playing Zork, a classic text adventure game. Your goal is to explore, solve puzzles, and find treasure.

IMPORTANT RULES:
- If a command fails ("I don't understand", "I can't see one"), DON'T repeat it - try something else
- If you've read something once, don't read it again - move on to explore
- When stuck, try moving: north, south, east, west, up, down
- Look for new areas to explore and new items to interact with

ZORK STRATEGY:
1. Open containers (mailbox, case, chest, door)
2. Take useful items (lamp, sword, rope, keys)
3. Read informational items once (leaflet, book, sign)
4. Explore new areas when current area is exhausted
5. Try "inventory" to see what you're carrying

Recent actions:
{context}

Current game state:
{game_output}

ANALYSIS: Look at your recent actions. If you've been repeating failed commands or reading the same thing multiple times, it's time to EXPLORE. Try going north, south, east, west, or interacting with something new.

What should you do next?
Respond with ONLY a single game command (1-2 words like "north", "take lamp", "open door").

COMMAND:"""

        try:
            payload = {
                "model": self.MODEL,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.8,  # Slightly higher for more creativity
                    "num_predict": 15    # Allow a bit more tokens
                }
            }

            response = requests.post(self.OLLAMA_URL, json=payload, timeout=90)
            response.raise_for_status()

            result = response.json()
            llm_response = result.get('response', '').strip()

            # Simple extraction - take first 1-2 words
            words = llm_response.lower().split()[:2]
            command = " ".join(words) if words else "look"

            return command

        except Exception as e:
            print(f"{self.RED}✗ LLM Error: {e}{self.RESET}")
            return "look"  # Fallback

    def send_command(self, command):
        """Send command to the game"""
        try:
            self.game_process.stdin.write(command + "\n")
            self.game_process.stdin.flush()
            return True
        except Exception as e:
            print(f"{self.RED}✗ Error sending command: {e}{self.RESET}")
            return False
    
    def play_game(self):
        """Main game loop - LLM controlled"""
        if not self.start_zork():
            return False

        print(f"🎮 Starting LLM-Controlled Zork\n")

        # Wait for initial output
        time.sleep(1)
        initial_output = self.get_game_output(timeout=10)
        self.display_output(initial_output)

        # Convert output lines to text for LLM
        game_text = "\n".join(initial_output)

        try:
            while self.turn_count < self.MAX_TURNS:
                self.turn_count += 1
                print(f"\n--- Turn {self.turn_count} ---")

                # Get command from LLM
                print(f"🤖 Querying LLM...")
                command = self.query_llm(game_text)
                print(f"🎯 {self.CYAN}LLM: '{command}'{self.RESET}")

                # Send command to game
                if not self.send_command(command):
                    break

                # Get and display response
                time.sleep(0.2)  # Brief pause for game to respond
                output_lines = self.get_game_output()
                self.display_output(output_lines)

                # Update game text and conversation history
                game_text = "\n".join(output_lines)
                self.conversation_history.append((command, game_text))

                # Keep history manageable
                if len(self.conversation_history) > 10:
                    self.conversation_history = self.conversation_history[-8:]

                time.sleep(1)  # Pause between turns

        except KeyboardInterrupt:
            print(f"\n{self.GREEN}Game interrupted by user{self.RESET}")
        except Exception as e:
            print(f"{self.RED}Error in game loop: {e}{self.RESET}")
        finally:
            self.cleanup()

        print(f"\n🎯 Game completed after {self.turn_count} turns")
        return True
    
    def cleanup(self):
        """Clean up game process"""
        if self.game_process:
            try:
                self.game_process.terminate()
                self.game_process.wait(timeout=5)
            except:
                self.game_process.kill()
            print(f"{self.GREEN}✓ Game process cleaned up{self.RESET}")

def main():
    print("Simple Zork LLM Player")
    print("=" * 30)

    # Check Ollama
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print(f"{ZorkLLMPlayer.GREEN}✓ Ollama is running{ZorkLLMPlayer.RESET}")
        else:
            print(f"{ZorkLLMPlayer.RED}✗ Ollama not responding{ZorkLLMPlayer.RESET}")
            sys.exit(1)
    except:
        print(f"{ZorkLLMPlayer.RED}✗ Cannot connect to Ollama{ZorkLLMPlayer.RESET}")
        sys.exit(1)

    player = ZorkLLMPlayer()
    player.play_game()

if __name__ == "__main__":
    main()

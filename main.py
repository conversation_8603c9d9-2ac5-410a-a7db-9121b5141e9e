#!/usr/bin/env python3
"""
Simple Zork LLM Player - Clean implementation following the specified flow
"""

import subprocess
import time
import requests
import sys
import re

class ZorkLLMPlayer:
    # Colors
    YELLOW = '\033[93m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    RED = '\033[91m'
    RESET = '\033[0m'
    
    # Configuration
    OLLAMA_URL = "http://localhost:11434/api/generate"
    MODEL = "deepseek-r1"
    MAX_TURNS = 100
    
    # Valid Zork commands from https://zork.fandom.com/wiki/Command_List
    VALID_COMMANDS = {
        # Movement
        'north', 'n', 'south', 's', 'east', 'e', 'west', 'w',
        'northeast', 'ne', 'northwest', 'nw', 'southeast', 'se', 'southwest', 'sw',
        'up', 'u', 'down', 'd', 'go', 'enter', 'in', 'out', 'climb',
        
        # Items
        'get', 'take', 'grab', 'drop', 'put', 'throw', 'open', 'close', 'read',
        'turn', 'move', 'attack', 'examine', 'inventory', 'i', 'eat', 'tie', 'pick',
        'break', 'kill', 'drink', 'smell', 'cut', 'listen',
        
        # Other
        'look', 'l', 'save', 'restore', 'restart', 'verbose', 'score', 'diagnose',
        'brief', 'superbrief', 'quit', 'q', 'g', 'hi', 'hello', 'shout', 'yell',
        'scream', 'pray', 'bar', 'jump', 'swing'
    }
    
    def __init__(self):
        self.game_process = None
        self.turn_count = 0
        
    def start_zork(self):
        """Start the Zork game process"""
        try:
            self.game_process = subprocess.Popen(
                ["zork"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=0  # Unbuffered
            )
            print(f"{self.GREEN}✓ Zork started{self.RESET}")
            return True
        except FileNotFoundError:
            print(f"{self.RED}✗ Zork not found. Install with: brew install zork{self.RESET}")
            return False
        except Exception as e:
            print(f"{self.RED}✗ Error starting Zork: {e}{self.RESET}")
            return False
    
    def read_game_output(self, timeout=10):
        """Read output from the game until we see the '>' prompt"""
        output = ""
        start_time = time.time()
        prompt_found = False

        while time.time() - start_time < timeout and not prompt_found:
            if self.game_process.poll() is not None:
                break

            try:
                # Check if data is available
                import select
                ready, _, _ = select.select([self.game_process.stdout], [], [], 0.1)
                if ready:
                    line = self.game_process.stdout.readline()
                    if line:
                        clean_line = line.rstrip()
                        if clean_line == ">":
                            # Found the prompt - game is ready for input
                            prompt_found = True
                            break
                        elif clean_line:  # Non-empty line that's not the prompt
                            print(f"{self.YELLOW}{clean_line}{self.RESET}")
                            output += line
                    else:
                        # No more data available right now
                        time.sleep(0.1)
                else:
                    # No data available, wait a bit
                    time.sleep(0.1)
            except Exception as e:
                print(f"Error reading: {e}")
                break

        if not prompt_found:
            print(f"{self.RED}⚠ Warning: Didn't find '>' prompt within {timeout}s{self.RESET}")

        return output.strip()
    
    def query_llm(self, game_output, context=""):
        """Send game output to LLM and get command response"""
        # Build system prompt with valid commands
        commands_list = ", ".join(sorted(self.VALID_COMMANDS))
        
        system_prompt = f"""You are playing Zork, a text adventure game. 

RULES:
1. Respond with ONLY a single game command
2. Use only these valid commands: {commands_list}
3. Commands can be 1-2 words (e.g., "north", "take lamp", "examine door")
4. NO explanations, reasoning, or multiple commands

Current game state:
{game_output}

{context}

Command:"""
        
        try:
            payload = {
                "model": self.MODEL,
                "prompt": system_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "num_predict": 10
                }
            }
            
            print(f"🤖 Querying LLM...")
            response = requests.post(self.OLLAMA_URL, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            llm_response = result.get('response', '').strip()
            
            # Extract clean command
            command = self.extract_command(llm_response)
            print(f"🎯 {self.CYAN}LLM: '{command}'{self.RESET}")
            
            return command
            
        except Exception as e:
            print(f"{self.RED}✗ LLM Error: {e}{self.RESET}")
            return "look"  # Fallback
    
    def extract_command(self, response):
        """Extract a clean command from LLM response"""
        # Remove reasoning tokens
        response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL | re.IGNORECASE)
        
        # Clean up
        response = response.lower().strip()
        response = response.strip('"\'')
        
        # Take first 1-2 words
        words = response.split()[:2]
        command = " ".join(words)
        
        # Validate against known commands
        if words and words[0] in self.VALID_COMMANDS:
            return command
        
        return "look"  # Safe fallback
    
    def send_command(self, command):
        """Send command to the game"""
        try:
            self.game_process.stdin.write(command + "\n")
            self.game_process.stdin.flush()
            return True
        except Exception as e:
            print(f"{self.RED}✗ Error sending command: {e}{self.RESET}")
            return False
    
    def play_game(self):
        """Main game loop following the specified flow"""
        if not self.start_zork():
            return False
        
        print(f"🎮 Starting Zork LLM Player\n")
        
        # Read initial game output
        game_output = self.read_game_output(timeout=10)
        context = ""
        
        try:
            while self.turn_count < self.MAX_TURNS:
                self.turn_count += 1
                print(f"\n--- Turn {self.turn_count} ---")
                
                # Send game output to LLM and get command
                command = self.query_llm(game_output, context)
                
                # Send command to game
                if not self.send_command(command):
                    break
                
                # Read game response
                game_output = self.read_game_output()
                
                # Build context for next turn (keep last few turns)
                if len(context.split('\n')) > 10:  # Limit context size
                    context = '\n'.join(context.split('\n')[-8:])
                context += f"\nPrevious: {command} -> {game_output[:100]}..."
                
                time.sleep(0.5)  # Brief pause
                
        except KeyboardInterrupt:
            print(f"\n{self.GREEN}Game interrupted by user{self.RESET}")
        finally:
            self.cleanup()
        
        print(f"\n🎯 Game completed after {self.turn_count} turns")
        return True
    
    def cleanup(self):
        """Clean up game process"""
        if self.game_process:
            try:
                self.game_process.terminate()
                self.game_process.wait(timeout=5)
            except:
                self.game_process.kill()
            print(f"{self.GREEN}✓ Game process cleaned up{self.RESET}")

def main():
    print("Simple Zork LLM Player")
    print("=" * 30)
    
    # Check Ollama
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print(f"{ZorkLLMPlayer.GREEN}✓ Ollama is running{ZorkLLMPlayer.RESET}")
        else:
            print(f"{ZorkLLMPlayer.RED}✗ Ollama not responding{ZorkLLMPlayer.RESET}")
            sys.exit(1)
    except:
        print(f"{ZorkLLMPlayer.RED}✗ Cannot connect to Ollama{ZorkLLMPlayer.RESET}")
        sys.exit(1)
    
    player = ZorkLLMPlayer()
    player.play_game()

if __name__ == "__main__":
    main()

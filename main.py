#!/usr/bin/env python3
"""
Zork LLM Player - Let an LLM play Zork text adventure game
Requires: ollama running locally with mistral-small3.1:latest model

File: main.py
"""

import subprocess
import time
import requests
import json
import re
import sys
import select
from threading import Thread, Event
import queue

class ZorkLLMPlayer:
    def __init__(self):
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model_name = "mistral-small3.1:latest"
        self.game_process = None
        self.conversation_history = []
        self.game_output_buffer = ""
        self.max_turns = 100  # Prevent infinite loops
        self.turn_count = 0
        
    def start_zork(self):
        """Start the Zork game process"""
        try:
            self.game_process = subprocess.Popen(
                ["zork"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )
            print("✓ Zork started successfully")
            return True
        except FileNotFoundError:
            print("✗ Error: 'zork' command not found. Make sure <PERSON>ork is installed and in your PATH.")
            return False
        except Exception as e:
            print(f"✗ Error starting Zork: {e}")
            return False
    
    def read_game_output(self, timeout=3):
        """Read output from the game with timeout"""
        output = ""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.game_process.poll() is not None:
                # Game has ended
                break
                
            try:
                # Check if there's data to read
                ready, _, _ = select.select([self.game_process.stdout], [], [], 0.1)
                if ready:
                    line = self.game_process.stdout.readline()
                    if line:
                        output += line
                        print(f"Game: {line.strip()}")
                    else:
                        break
                else:
                    # No more immediate output, but wait a bit more
                    time.sleep(0.1)
            except Exception as e:
                print(f"Error reading game output: {e}")
                break
        
        return output.strip()
    
    def send_command_to_game(self, command):
        """Send a command to the game"""
        if self.game_process and self.game_process.poll() is None:
            try:
                print(f"Player: {command}")
                self.game_process.stdin.write(command + "\n")
                self.game_process.stdin.flush()
                return True
            except Exception as e:
                print(f"Error sending command: {e}")
                return False
        return False
    
    def query_llm(self, game_state, conversation_context=""):
        """Query the LLM for the next command"""
        
        # Build the prompt
        system_prompt = """You are playing Zork, a classic text adventure game. 
Your goal is to explore, solve puzzles, and complete the adventure.

Rules:
1. Respond with ONLY a single game command (e.g., "north", "take lamp", "examine door")
2. Do NOT include explanations, reasoning, or multiple commands
3. Use standard adventure game commands: directions (north, south, east, west, up, down), actions (take, drop, examine, open, close, use), etc.
4. Pay attention to the game's responses to understand your environment and available actions

Current game state:"""

        prompt = f"{system_prompt}\n\n{game_state}\n\nWhat is your next command?"
        
        if conversation_context:
            prompt = f"Previous context:\n{conversation_context}\n\n{prompt}"
        
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": 50
                }
            }
            
            print("🤖 Querying LLM...")
            response = requests.post(self.ollama_url, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            llm_response = result.get('response', '').strip()
            
            # Extract just the command (remove any explanations)
            command = self.extract_command(llm_response)
            return command
            
        except requests.exceptions.RequestException as e:
            print(f"✗ Error querying LLM: {e}")
            return None
        except Exception as e:
            print(f"✗ Unexpected error: {e}")
            return None
    
    def extract_command(self, llm_response):
        """Extract a clean command from LLM response"""
        # Remove common prefixes and explanations
        response = llm_response.lower().strip()
        
        # Remove quotes if present
        if response.startswith('"') and response.endswith('"'):
            response = response[1:-1]
        if response.startswith("'") and response.endswith("'"):
            response = response[1:-1]
        
        # Look for common command patterns and take the first line/sentence
        lines = response.split('\n')
        if lines:
            command = lines[0].strip()
            
            # Remove explanatory prefixes
            prefixes_to_remove = [
                "i will", "i'll", "let me", "i should", "i would", "i want to",
                "command:", "action:", "next:", ">"
            ]
            
            for prefix in prefixes_to_remove:
                if command.startswith(prefix):
                    command = command[len(prefix):].strip()
        
            return command
        
        return response
    
    def build_conversation_context(self, max_entries=5):
        """Build recent conversation context for the LLM"""
        if len(self.conversation_history) <= max_entries:
            entries = self.conversation_history
        else:
            entries = self.conversation_history[-max_entries:]
        
        context = ""
        for entry in entries:
            context += f"Command: {entry['command']}\nResult: {entry['result']}\n\n"
        
        return context.strip()
    
    def is_game_over(self, game_output):
        """Check if the game has ended"""
        end_phrases = [
            "game over",
            "you have died",
            "you are dead",
            "congratulations",
            "you have won",
            "*** you have died ***",
            "would you like to restart"
        ]
        
        output_lower = game_output.lower()
        return any(phrase in output_lower for phrase in end_phrases)
    
    def play_game(self):
        """Main game loop"""
        if not self.start_zork():
            return False
        
        print("🎮 Starting LLM-powered Zork adventure!\n")
        
        # Get initial game output
        initial_output = self.read_game_output(timeout=5)
        if not initial_output:
            print("✗ No initial output from game")
            return False
        
        current_state = initial_output
        
        try:
            while self.turn_count < self.max_turns:
                self.turn_count += 1
                print(f"\n--- Turn {self.turn_count} ---")
                
                # Check if game is over
                if self.is_game_over(current_state):
                    print("🏁 Game appears to be over!")
                    break
                
                # Get command from LLM
                context = self.build_conversation_context()
                command = self.query_llm(current_state, context)
                
                if not command:
                    print("✗ Failed to get command from LLM")
                    break
                
                # Send command to game
                if not self.send_command_to_game(command):
                    print("✗ Failed to send command to game")
                    break
                
                # Get game response
                game_response = self.read_game_output()
                if not game_response:
                    print("⚠ No response from game, continuing...")
                    game_response = "(no response)"
                
                # Store in conversation history
                self.conversation_history.append({
                    'command': command,
                    'result': game_response
                })
                
                current_state = game_response
                time.sleep(1)  # Brief pause between turns
                
        except KeyboardInterrupt:
            print("\n🛑 Game interrupted by user")
        except Exception as e:
            print(f"✗ Unexpected error during gameplay: {e}")
        finally:
            self.cleanup()
        
        print(f"\n🎯 Game completed after {self.turn_count} turns")
        return True
    
    def cleanup(self):
        """Clean up resources"""
        if self.game_process:
            try:
                self.game_process.terminate()
                self.game_process.wait(timeout=5)
            except:
                self.game_process.kill()
            print("✓ Game process cleaned up")

def main():
    print("Zork LLM Player")
    print("=" * 40)
    print("Requirements:")
    print("- Ollama running locally (ollama serve)")
    print("- mistral-small3.1:latest model available")
    print("- Zork installed and accessible via 'zork' command")
    print()
    
    # Check if ollama is running
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✓ Ollama is running")
        else:
            print("✗ Ollama is not responding properly")
            sys.exit(1)
    except:
        print("✗ Cannot connect to Ollama. Make sure it's running with 'ollama serve'")
        sys.exit(1)
    
    player = ZorkLLMPlayer()
    
    try:
        player.play_game()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    finally:
        player.cleanup()

if __name__ == "__main__":
    main()
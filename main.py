#!/usr/bin/env python3
"""
Zork LLM Player - Let an LLM play Zork text adventure game
Requires: ollama running locally with mistral-small3.1:latest model

File: main.py
"""

import subprocess
import time
import requests
import sys
import select
from datetime import datetime

class ZorkLLMPlayer:
    # Color constants for terminal output
    YELLOW = '\033[93m'      # Game output color
    LIGHT_BLUE = '\033[94m'  # LLM response color
    GREEN = '\033[92m'       # Success messages
    RED = '\033[91m'         # Error messages
    RESET = '\033[0m'        # Reset to default color
    # Configuration constants
    DEFAULT_OLLAMA_URL = "http://localhost:11434/api/generate"
    DEFAULT_MODEL = "mistral-small3.1:latest"
    DEFAULT_MAX_TURNS = 100
    DEFAULT_TIMEOUT = 3
    INITIAL_TIMEOUT = 15  # Increased for long intro text
    CLEANUP_TIMEOUT = 5
    OLLAMA_CHECK_TIMEOUT = 5

    # LLM Configuration
    LLM_TEMPERATURE = 0.7
    LLM_TOP_P = 0.9
    LLM_MAX_TOKENS = 50
    LLM_CONNECT_TIMEOUT = 10
    LLM_READ_TIMEOUT = 120

    # Timing constants
    SELECT_TIMEOUT = 0.1
    SLEEP_INTERVAL = 0.1
    TURN_PAUSE = 1

    def __init__(self):
        self.ollama_url = self.DEFAULT_OLLAMA_URL
        self.model_name = self.DEFAULT_MODEL
        self.game_process = None
        self.conversation_history = []
        self.max_turns = self.DEFAULT_MAX_TURNS
        self.turn_count = 0

        # Setup logging
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_filename = f"zork_game_log_{timestamp}.txt"
        self.log_file = None
        self.setup_logging()

    def setup_logging(self):
        """Setup the game log file"""
        try:
            self.log_file = open(self.log_filename, 'w', encoding='utf-8')
            header = f"""ZORK LLM PLAYER - GAME LOG
=====================================
Timestamp: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
Model: {self.model_name}
Max Turns: {self.max_turns}
=====================================

"""
            self.log_file.write(header)
            self.log_file.flush()
            print(f"📝 Game log will be saved to: {self.log_filename}")
        except Exception as e:
            print(f"⚠️ Warning: Could not create log file: {e}")
            self.log_file = None

    def log_to_file(self, message, category="INFO"):
        """Write a message to the log file"""
        if self.log_file:
            try:
                timestamp = datetime.now().strftime("%H:%M:%S")
                log_entry = f"[{timestamp}] {category}: {message}\n"
                self.log_file.write(log_entry)
                self.log_file.flush()
            except Exception as e:
                print(f"⚠️ Warning: Could not write to log file: {e}")

    def log_game_interaction(self, turn_number, command, game_output, llm_response_raw=None):
        """Log a complete game interaction"""
        if self.log_file:
            try:
                separator = "=" * 50
                log_entry = f"""
{separator}
TURN {turn_number}
{separator}

GAME OUTPUT:
{game_output}

LLM RAW RESPONSE:
{llm_response_raw if llm_response_raw else "N/A"}

COMMAND SENT:
{command}

"""
                self.log_file.write(log_entry)
                self.log_file.flush()
            except Exception as e:
                print(f"⚠️ Warning: Could not log game interaction: {e}")
        
    def start_zork(self):
        """Start the Zork game process"""
        try:
            self.game_process = subprocess.Popen(
                ["zork"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )
            print(f"{self.GREEN}✓ Zork started successfully{self.RESET}")
            self.log_to_file("Zork game started successfully", "SYSTEM")
            # Give Zork a moment to initialize
            time.sleep(0.5)
            return True
        except FileNotFoundError:
            error_msg = "Error: 'zork' command not found. Make sure Zork is installed and in your PATH."
            print(f"{self.RED}✗ {error_msg}{self.RESET}")
            self.log_to_file(error_msg, "ERROR")
            return False
        except Exception as e:
            error_msg = f"Error starting Zork: {e}"
            print(f"{self.RED}✗ {error_msg}{self.RESET}")
            self.log_to_file(error_msg, "ERROR")
            return False
    
    def read_game_output(self, timeout=None):
        """
        Read output from the game with timeout - enhanced for long text and formatting.

        This function addresses multiple issues:
        1. Captures complete long text (like Zork's intro) without truncation
        2. Preserves original formatting (indentation, spacing) from the game
        3. Filters out game prompts (">") while keeping meaningful content
        4. Uses larger buffers and more patient timing for complex output

        Returns clean, formatted game text ready for display and LLM processing.
        """
        if timeout is None:
            timeout = self.DEFAULT_TIMEOUT

        output = ""
        start_time = time.time()
        last_output_time = start_time
        no_output_threshold = 3.0  # Wait 3 seconds after last output before giving up (increased for long text)
        printed_lines = set()  # Track what we've already printed to avoid duplicates
        consecutive_empty_reads = 0
        max_consecutive_empty_reads = 30  # Allow more empty reads for long text

        while time.time() - start_time < timeout:
            if self.game_process.poll() is not None:
                # Game has ended
                break

            try:
                # Check if there's data to read
                ready, _, _ = select.select([self.game_process.stdout], [], [], 0.1)
                if ready:
                    # Try to read a line, but don't block if incomplete
                    try:
                        # Set stdout to non-blocking temporarily
                        import fcntl
                        import os
                        fd = self.game_process.stdout.fileno()
                        fl = fcntl.fcntl(fd, fcntl.F_GETFL)
                        fcntl.fcntl(fd, fcntl.F_SETFL, fl | os.O_NONBLOCK)

                        # Read available data - larger buffer for long text
                        chunk = self.game_process.stdout.read(4096)

                        # Restore blocking mode
                        fcntl.fcntl(fd, fcntl.F_SETFL, fl)

                        if chunk:
                            output += chunk
                            last_output_time = time.time()
                            consecutive_empty_reads = 0  # Reset counter on successful read

                            # Process complete lines for immediate display
                            lines = output.split('\n')
                            for line in lines[:-1]:  # All but the last (potentially incomplete) line
                                # Preserve formatting but filter out game prompts
                                # Only strip trailing whitespace, keep leading whitespace for formatting
                                line = line.rstrip()
                                # Filter out the game prompt ">" but keep formatted empty lines
                                if line != ">" and line not in printed_lines:
                                    print(f"{self.YELLOW}{line}{self.RESET}")
                                    self.log_to_file(f"Game output: {line}", "GAME")
                                    printed_lines.add(line)
                        else:
                            # No data available, increment counter
                            consecutive_empty_reads += 1
                            # Check timeout with more patience for long text
                            if (time.time() - last_output_time > no_output_threshold and
                                output and consecutive_empty_reads > max_consecutive_empty_reads):
                                break

                    except (BlockingIOError, OSError):
                        # No data available right now, that's okay
                        consecutive_empty_reads += 1
                        if (time.time() - last_output_time > no_output_threshold and
                            output and consecutive_empty_reads > max_consecutive_empty_reads):
                            break
                else:
                    # No data ready, check if we should give up
                    consecutive_empty_reads += 1
                    if (time.time() - last_output_time > no_output_threshold and
                        output and consecutive_empty_reads > max_consecutive_empty_reads):
                        break
                    # Wait a bit more
                    time.sleep(self.SLEEP_INTERVAL)
            except Exception as e:
                print(f"Error reading game output: {e}")
                break

        # Print any remaining incomplete line
        if output:
            lines = output.split('\n')
            last_line = lines[-1].rstrip()  # Only strip trailing whitespace
            # Filter out the game prompt ">" from the final line as well
            if last_line != ">" and last_line not in printed_lines:
                print(f"{self.YELLOW}{last_line}{self.RESET}")
                self.log_to_file(f"Game output: {last_line}", "GAME")

        # Clean the output before returning - remove prompts and extra whitespace
        cleaned_output = self._clean_game_output(output)
        return cleaned_output

    def _clean_game_output(self, output):
        """Clean game output by removing prompts while preserving formatting"""
        if not output:
            return ""

        # Split into lines and process each one
        lines = output.split('\n')
        cleaned_lines = []

        for line in lines:
            # Only strip trailing whitespace to preserve indentation/formatting
            line = line.rstrip()
            # Skip game prompts but keep empty lines (they might be formatting)
            if line != ">":
                cleaned_lines.append(line)

        # Join back together
        cleaned = '\n'.join(cleaned_lines)

        # Remove any remaining isolated ">" characters that might be embedded
        # But be more careful to preserve formatting
        cleaned = cleaned.replace('\n>\n', '\n\n').replace('\n>', '').replace('>\n', '\n')

        # Remove excessive blank lines at start/end but preserve internal formatting
        cleaned = cleaned.strip()

        return cleaned
    
    def send_command_to_game(self, command):
        """Send a command to the game"""
        if self.game_process and self.game_process.poll() is None:
            try:
                self.log_to_file(f"Sending command: {command}", "PLAYER")
                self.game_process.stdin.write(command + "\n")
                self.game_process.stdin.flush()
                return True
            except Exception as e:
                print(f"Error sending command: {e}")
                return False
        return False
    
    def query_llm(self, game_state, conversation_context="", max_retries=3):
        """Query the LLM for the next command with retry logic"""

        # Build the prompt
        system_prompt = """You are playing Zork, a classic text adventure game.
Your goal is to explore, solve puzzles, and complete the adventure.

Rules:
1. Respond with ONLY a single game command (e.g., "north", "take lamp", "examine door") which can be up to two words long
2. Do NOT include explanations, reasoning, or multiple commands
3. Use standard adventure game commands: directions (north, south, east, west, up, down), actions (take, drop, examine, open, close, use), etc.
4. Pay attention to the game's responses to understand your environment and available actions

Current game state:"""

        prompt = f"{system_prompt}\n\n{game_state}\n\nWhat is your next command?"

        if conversation_context:
            prompt = f"Previous context:\n{conversation_context}\n\n{prompt}"

        # Log the full prompt being sent to LLM
        self.log_to_file(f"Sending prompt to LLM:\n{prompt}", "LLM_PROMPT")

        for attempt in range(max_retries):
            try:
                payload = {
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": self.LLM_TEMPERATURE,
                        "top_p": self.LLM_TOP_P,
                        "num_predict": self.LLM_MAX_TOKENS
                    }
                }

                print(f"🤖 Querying LLM (attempt {attempt + 1}/{max_retries})...")
                self.log_to_file(f"Querying LLM (attempt {attempt + 1}/{max_retries})", "LLM")

                # Increased timeout and added connection timeout
                response = requests.post(
                    self.ollama_url,
                    json=payload,
                    timeout=(self.LLM_CONNECT_TIMEOUT, self.LLM_READ_TIMEOUT),
                    headers={'Content-Type': 'application/json'}
                )
                response.raise_for_status()

                result = response.json()
                llm_response = result.get('response', '').strip()

                # Log the raw LLM response
                self.log_to_file(f"LLM raw response: {llm_response}", "LLM_RESPONSE")

                if llm_response:
                    # Extract just the command (remove any explanations)
                    command = self.extract_command(llm_response)
                    print(f"🎯 {self.LIGHT_BLUE}LLM: '{command}'{self.RESET}")
                    self.log_to_file(f"Extracted command: {command}", "LLM_COMMAND")
                    return command, llm_response  # Return both for logging
                else:
                    print("⚠️ LLM returned empty response")
                    self.log_to_file("LLM returned empty response", "WARNING")

            except requests.exceptions.ReadTimeout:
                error_msg = f"Read timeout on attempt {attempt + 1}"
                print(f"⏰ {error_msg}")
                self.log_to_file(error_msg, "ERROR")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5
                    print(f"   Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    print("✗ Max retries reached for read timeout")
                    self.log_to_file("Max retries reached for read timeout", "ERROR")

            except requests.exceptions.ConnectionError as e:
                error_msg = f"Connection error on attempt {attempt + 1}: {e}"
                print(f"🔌 {error_msg}")
                self.log_to_file(error_msg, "ERROR")
                if attempt < max_retries - 1:
                    print("   Retrying in 3 seconds...")
                    time.sleep(3)
                else:
                    print("✗ Max retries reached for connection error")
                    self.log_to_file("Max retries reached for connection error", "ERROR")

            except requests.exceptions.RequestException as e:
                error_msg = f"Request error on attempt {attempt + 1}: {e}"
                print(f"✗ {error_msg}")
                self.log_to_file(error_msg, "ERROR")
                if attempt < max_retries - 1:
                    print("   Retrying in 2 seconds...")
                    time.sleep(2)
                else:
                    print("✗ Max retries reached for request error")
                    self.log_to_file("Max retries reached for request error", "ERROR")

            except Exception as e:
                error_msg = f"Unexpected error on attempt {attempt + 1}: {e}"
                print(f"✗ {error_msg}")
                self.log_to_file(error_msg, "ERROR")
                break

        # Fallback commands if all retries fail
        fallback_commands = ["look", "inventory", "north", "examine room"]
        fallback_command = fallback_commands[self.turn_count % len(fallback_commands)]
        print(f"🆘 {self.LIGHT_BLUE}Using fallback command: '{fallback_command}'{self.RESET}")
        self.log_to_file(f"Using fallback command: {fallback_command}", "FALLBACK")
        return fallback_command, None
    
    def extract_command(self, llm_response):
        """Extract a clean command from LLM response"""
        # Remove common prefixes and explanations
        response = llm_response.lower().strip()
        
        # Remove quotes if present
        if response.startswith('"') and response.endswith('"'):
            response = response[1:-1]
        if response.startswith("'") and response.endswith("'"):
            response = response[1:-1]
        
        # Look for common command patterns and take the first line/sentence
        lines = response.split('\n')
        if lines:
            command = lines[0].strip()
            
            # Remove explanatory prefixes
            prefixes_to_remove = [
                "i will", "i'll", "let me", "i should", "i would", "i want to",
                "command:", "action:", "next:", ">"
            ]
            
            for prefix in prefixes_to_remove:
                if command.startswith(prefix):
                    command = command[len(prefix):].strip()
        
            return command
        
        return response
    
    def build_conversation_context(self, max_entries=5):
        """Build recent conversation context for the LLM"""
        if len(self.conversation_history) <= max_entries:
            entries = self.conversation_history
        else:
            entries = self.conversation_history[-max_entries:]
        
        context = ""
        for entry in entries:
            context += f"Command: {entry['command']}\nResult: {entry['result']}\n\n"
        
        return context.strip()
    
    def is_game_over(self, game_output):
        """Check if the game has ended"""
        end_phrases = [
            "game over",
            "you have died",
            "you are dead",
            "congratulations",
            "you have won",
            "*** you have died ***",
            "would you like to restart"
        ]
        
        output_lower = game_output.lower()
        return any(phrase in output_lower for phrase in end_phrases)
    
    def play_game(self):
        """Main game loop"""
        if not self.start_zork():
            return False

        print("🎮 Starting LLM-powered Zork adventure!\n")

        # Get initial game output
        initial_output = self.read_game_output(timeout=self.INITIAL_TIMEOUT)
        if not initial_output:
            print("✗ No initial output from game")
            self.log_to_file("No initial output from game", "ERROR")
            return False

        current_state = initial_output
        self.log_to_file(f"Initial game state:\n{initial_output}", "GAME_START")

        try:
            while self.turn_count < self.max_turns:
                self.turn_count += 1
                print(f"\n--- Turn {self.turn_count} ---")

                # Check if game is over
                if self.is_game_over(current_state):
                    print("🏁 Game appears to be over!")
                    break

                # Get command from LLM
                context = self.build_conversation_context()
                result = self.query_llm(current_state, context)

                if isinstance(result, tuple):
                    command, llm_raw_response = result
                else:
                    command, llm_raw_response = result, None

                if not command:
                    print("✗ Failed to get command from LLM after all retries")
                    self.log_to_file("Failed to get command from LLM after all retries", "ERROR")
                    break

                # Send command to game
                if not self.send_command_to_game(command):
                    print("✗ Failed to send command to game")
                    self.log_to_file("Failed to send command to game", "ERROR")
                    break

                # Get game response
                game_response = self.read_game_output()
                if not game_response:
                    print("⚠ No response from game, continuing...")
                    game_response = "(no response)"

                # Log the complete interaction
                self.log_game_interaction(self.turn_count, command, game_response, llm_raw_response)

                # Store in conversation history
                self.conversation_history.append({
                    'command': command,
                    'result': game_response
                })

                current_state = game_response
                time.sleep(self.TURN_PAUSE)  # Brief pause between turns

        except KeyboardInterrupt:
            print("\n🛑 Game interrupted by user")
            self.log_to_file("Game interrupted by user (Ctrl+C)", "SYSTEM")
        except Exception as e:
            error_msg = f"Unexpected error during gameplay: {e}"
            print(f"✗ {error_msg}")
            self.log_to_file(error_msg, "ERROR")
        finally:
            self.cleanup()

        final_msg = f"Game completed after {self.turn_count} turns"
        print(f"\n🎯 {final_msg}")
        self.log_to_file(final_msg, "SYSTEM")
        return True
    
    def cleanup(self):
        """Clean up resources"""
        if self.game_process:
            try:
                self.game_process.terminate()
                self.game_process.wait(timeout=self.CLEANUP_TIMEOUT)
            except Exception:
                self.game_process.kill()
            print(f"{self.GREEN}✓ Game process cleaned up{self.RESET}")
            self.log_to_file("Game process cleaned up", "SYSTEM")

        if self.log_file:
            try:
                self.log_file.write(f"\n\nGAME SESSION ENDED\n")
                self.log_file.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                self.log_file.write(f"Total turns: {self.turn_count}\n")
                self.log_file.close()
                print(f"{self.GREEN}✓ Game log saved to: {self.log_filename}{self.RESET}")
            except Exception as e:
                print(f"⚠️ Warning: Could not close log file properly: {e}")
            finally:
                self.log_file = None

def main():
    print("Zork LLM Player")
    print("=" * 40)
    print("Requirements:")
    print("- Ollama running locally (ollama serve)")
    print(f"- {ZorkLLMPlayer.DEFAULT_MODEL} model available")
    print("- Zork installed and accessible via 'zork' command")
    print()

    # Check if ollama is running
    try:
        response = requests.get(f"{ZorkLLMPlayer.DEFAULT_OLLAMA_URL.replace('/api/generate', '/api/tags')}",
                              timeout=ZorkLLMPlayer.OLLAMA_CHECK_TIMEOUT)
        if response.status_code == 200:
            print(f"{ZorkLLMPlayer.GREEN}✓ Ollama is running{ZorkLLMPlayer.RESET}")
        else:
            print(f"{ZorkLLMPlayer.RED}✗ Ollama is not responding properly{ZorkLLMPlayer.RESET}")
            sys.exit(1)
    except:
        print(f"{ZorkLLMPlayer.RED}✗ Cannot connect to Ollama. Make sure it's running with 'ollama serve'{ZorkLLMPlayer.RESET}")
        sys.exit(1)

    player = ZorkLLMPlayer()

    try:
        player.play_game()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    finally:
        player.cleanup()

if __name__ == "__main__":
    main()
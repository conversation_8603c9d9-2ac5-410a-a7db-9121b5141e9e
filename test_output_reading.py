#!/usr/bin/env python3
"""
Test script to simulate <PERSON>or<PERSON>'s output pattern and verify our reading logic works correctly
"""

import subprocess
import time
import sys

def create_test_script():
    """Create a test script that simulates Zork's output pattern"""
    test_script = '''#!/usr/bin/env python3
import time
import sys

# Simulate Zork's initial output pattern
print("Welcome to Dungeon.\\t\\t\\tThis version created 11-MAR-91.")
sys.stdout.flush()

# Simulate a small delay like Zork might have
time.sleep(0.5)

print("You are in an open field west of a big white house with a boarded")
print("front door.")
print("There is a small mailbox here.")
sys.stdout.flush()

# Keep the process alive longer to allow reading
time.sleep(3)

# Wait for input (simulate game waiting for command)
try:
    input()
except:
    pass
'''
    
    with open('test_zork_sim.py', 'w') as f:
        f.write(test_script)

def test_output_reading():
    """Test our improved output reading logic"""
    print("Testing improved output reading logic...")
    print("=" * 50)
    
    # Create the test script
    create_test_script()
    
    try:
        # Import our improved reading function
        import sys
        sys.path.insert(0, '.')
        from main import ZorkLLMPlayer
        
        # Create a player instance
        player = ZorkLLMPlayer()
        
        # Start our test "game"
        test_process = subprocess.Popen(
            ["python3", "test_zork_sim.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            bufsize=1
        )
        
        # Temporarily replace the game process
        original_process = player.game_process
        player.game_process = test_process
        
        print("Reading initial output with improved logic...")
        print("-" * 30)
        
        # Test the improved reading
        output = player.read_game_output(timeout=8)
        
        print("-" * 30)
        print(f"Complete output captured ({len(output)} characters):")
        print(repr(output))
        print("-" * 30)
        print("Formatted output:")
        print(output)
        
        # Check if we got the complete output
        expected_parts = [
            "Welcome to Dungeon",
            "You are in an open field",
            "There is a small mailbox here"
        ]
        
        success = all(part in output for part in expected_parts)
        
        if success:
            print(f"\n{player.GREEN}✓ SUCCESS: All expected output parts captured!{player.RESET}")
        else:
            print(f"\n{player.RED}✗ FAILURE: Some output parts missing{player.RESET}")
            for part in expected_parts:
                status = "✓" if part in output else "✗"
                print(f"  {status} '{part}'")
        
        # Cleanup
        test_process.terminate()
        player.game_process = original_process
        
    except Exception as e:
        print(f"Error during test: {e}")
    finally:
        # Clean up test file
        import os
        try:
            os.remove('test_zork_sim.py')
        except:
            pass

if __name__ == "__main__":
    test_output_reading()

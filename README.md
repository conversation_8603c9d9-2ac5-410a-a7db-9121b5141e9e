# PyZork - AI-Powered Zork Player

An automated Zork player that uses Large Language Models (LLMs) to play the classic text adventure game autonomously.

## Project Structure

```
pyzork/
├── main.py              # Main game logic and ZorkLLMPlayer class
├── run.sh               # Shell script for environment setup and execution
├── requirements.txt     # Python dependencies
├── venv/               # Virtual environment (auto-created)
└── zork_game_log_*.txt # Game session logs (auto-generated)
```

## Features

- **Autonomous Gameplay**: AI interprets game states and generates commands
- **Comprehensive Logging**: Detailed session logs with timestamps and categorization
- **Robust Error Handling**: Retry logic with fallback commands
- **Environment Management**: Automated virtual environment setup
- **Real-time Monitoring**: Live game output and AI decision tracking

## Requirements

- **Python 3.x** with standard library
- **Ollama** running locally with `mistral-small3.1:latest` model
- **Zork** game executable accessible via `zork` command
- **requests** library (auto-installed)

## Installation

### 1. Install Zork
```bash
# macOS
brew install zork

# Ubuntu/Debian
sudo apt-get install zork
```

### 2. Install and Setup Ollama
```bash
# Install Ollama (visit https://ollama.ai for instructions)
# Then pull the required model
ollama pull mistral-small3.1:latest

# Start Ollama server
ollama serve
```

### 3. Clone and Setup Project
```bash
git clone <repository-url>
cd pyzork
```

## Usage

### Quick Start
```bash
./run.sh
```

The shell script will automatically:
- Create and activate a virtual environment
- Install Python dependencies
- Check system requirements
- Run the game

### Manual Execution
```bash
# Activate virtual environment
source venv/bin/activate

# Install dependencies (first time only)
pip install -r requirements.txt

# Run the game
python main.py
```

## Configuration

The game can be configured by modifying variables in `main.py`:

```python
class ZorkLLMPlayer:
    def __init__(self):
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model_name = "mistral-small3.1:latest"  # Change model here
        self.max_turns = 100  # Maximum game turns
```

## Game Logs

Each session generates a detailed log file with format `zork_game_log_YYYYMMDD_HHMMSS.txt` containing:

- System events and errors
- Game output and player commands
- LLM prompts and responses
- Turn-by-turn interaction details

## Architecture

### Main Components

1. **ZorkLLMPlayer Class** (`main.py`)
   - Game process management
   - LLM integration with Ollama
   - Command processing and extraction
   - Comprehensive logging system
   - Error handling and recovery

2. **Environment Setup** (`run.sh`)
   - Virtual environment management
   - Dependency installation
   - System requirement checks
   - Graceful execution handling

### Key Features

- **Non-blocking I/O**: Uses `select()` for responsive game interaction
- **Retry Logic**: Multiple attempts with exponential backoff for LLM queries
- **Fallback Commands**: Predefined commands when LLM fails
- **Context Management**: Maintains conversation history for better AI decisions
- **Process Cleanup**: Proper resource management and cleanup

## Troubleshooting

### Common Issues

1. **"zork command not found"**
   - Install Zork using your package manager
   - Ensure Zork is in your system PATH

2. **"Cannot connect to Ollama"**
   - Start Ollama server: `ollama serve`
   - Verify model is available: `ollama list`
   - Pull required model: `ollama pull mistral-small3.1:latest`

3. **LLM timeout errors**
   - Check Ollama server status
   - Verify model is loaded and responsive
   - Increase timeout values in `main.py` if needed

### Debug Mode

For debugging, you can run the Python script directly to see detailed output:

```bash
source venv/bin/activate
python main.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

[Add your license information here]

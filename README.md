# PyZork - AI-Powered Zork Player

An automated Zork player that uses Large Language Models (LLMs) to play the classic text adventure game autonomously.

## Project Structure

```
pyzork/
├── main.py              # Main game logic and ZorkLLMPlayer class
├── run.sh               # Shell script for environment setup and execution
├── requirements.txt     # Python dependencies
├── venv/               # Virtual environment (auto-created)
└── zork_game_log_*.txt # Game session logs (auto-generated)
```

## Features

- **Autonomous Gameplay**: AI interprets game states and generates commands
- **Colored Terminal Output**: Game responses in yellow, LLM responses in light blue
- **Clean Output Processing**: Filters out game prompts while preserving text formatting
- **Comprehensive Logging**: Detailed session logs with timestamps and categorization
- **Robust Error Handling**: Retry logic with fallback commands
- **Environment Management**: Automated virtual environment setup
- **Real-time Monitoring**: Live game output and AI decision tracking

## Requirements

- **Python 3.x** with standard library
- **Ollama** running locally with `mistral-small3.1:latest` model
- **Zork** game executable accessible via `zork` command
- **requests** library (auto-installed)

## Installation

### 1. Install Zork
```bash
# macOS
brew install zork

# Ubuntu/Debian
sudo apt-get install zork
```

### 2. Install and Setup Ollama
```bash
# Install Ollama (visit https://ollama.ai for instructions)
# Then pull the required model
ollama pull mistral-small3.1:latest

# Start Ollama server
ollama serve
```

### 3. Clone and Setup Project
```bash
git clone <repository-url>
cd pyzork
```

## Usage

### Quick Start
```bash
./run.sh
```

The shell script will automatically:
- Create and activate a virtual environment
- Install Python dependencies
- Check system requirements
- Run the game

### Manual Execution
```bash
# Activate virtual environment
source venv/bin/activate

# Install dependencies (first time only)
pip install -r requirements.txt

# Run the game
python main.py
```

## Configuration

The game can be configured by modifying class constants in `main.py`:

```python
class ZorkLLMPlayer:
    # Configuration constants - modify these to customize behavior
    DEFAULT_MODEL = "mistral-small3.1:latest"  # Change model here
    DEFAULT_MAX_TURNS = 100  # Maximum game turns
    LLM_TEMPERATURE = 0.7   # AI creativity (0.0-1.0)
    LLM_TOP_P = 0.9         # AI response diversity
    DEFAULT_TIMEOUT = 3     # Game output timeout

    # Color constants for terminal output
    YELLOW = '\033[93m'      # Game output color
    LIGHT_BLUE = '\033[94m'  # LLM response color
    GREEN = '\033[92m'       # Success messages
    RED = '\033[91m'         # Error messages
```

## Visual Features

### Colored Terminal Output
The game uses color-coded output for better readability:

- **🟡 Yellow**: Game responses from Zork (without "Game:" prefix)
- **🔵 Light Blue**: LLM responses and commands
- **🟢 Green**: Success messages and confirmations
- **🔴 Red**: Error messages and warnings

### Game Logs

Each session generates a detailed log file with format `zork_game_log_YYYYMMDD_HHMMSS.txt` containing:

- System events and errors
- Game output and player commands
- LLM prompts and responses
- Turn-by-turn interaction details

## Architecture

### Main Components

1. **ZorkLLMPlayer Class** (`main.py`)
   - Game process management
   - LLM integration with Ollama
   - Command processing and extraction
   - Comprehensive logging system
   - Error handling and recovery

2. **Environment Setup** (`run.sh`)
   - Virtual environment management
   - Dependency installation
   - System requirement checks
   - Graceful execution handling

### Key Features

- **Non-blocking I/O**: Uses `select()` for responsive game interaction
- **Enhanced Output Reading**: Captures complete long text with proper formatting preservation
- **Smart Prompt Filtering**: Automatically removes game prompts (">") from output and LLM input
- **Retry Logic**: Multiple attempts with exponential backoff for LLM queries
- **Fallback Commands**: Predefined commands when LLM fails
- **Context Management**: Maintains conversation history for better AI decisions
- **Process Cleanup**: Proper resource management and cleanup

## Troubleshooting

### Common Issues

1. **"zork command not found"**
   - Install Zork using your package manager
   - Ensure Zork is in your system PATH

2. **"Cannot connect to Ollama"**
   - Start Ollama server: `ollama serve`
   - Verify model is available: `ollama list`
   - Pull required model: `ollama pull mistral-small3.1:latest`

3. **LLM timeout errors**
   - Check Ollama server status
   - Verify model is loaded and responsive
   - Increase timeout values in `main.py` if needed

### Debug Mode

For debugging, you can run the Python script directly to see detailed output:

```bash
source venv/bin/activate
python main.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

[Add your license information here]

#!/bin/bash

# Zork LLM Player - Shell Script Runner
# This script sets up the virtual environment and runs the main Python game

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "Zork LLM Player - Environment Setup"
echo "===================================="

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_info "Virtual environment not found. Creating one..."
    python3 -m venv venv
    print_status "Virtual environment created"
fi

# Activate virtual environment
print_info "Activating virtual environment..."
source venv/bin/activate
print_status "Virtual environment activated"

# Check if requirements are installed
if [ ! -f "venv/requirements_installed.flag" ]; then
    print_info "Installing requirements..."
    pip install -r requirements.txt
    touch venv/requirements_installed.flag
    print_status "Requirements installed"
else
    print_status "Requirements already installed"
fi

# Check if Zork is available
if ! command -v zork &> /dev/null; then
    print_warning "Zork command not found in PATH"
    print_info "Please install Zork or ensure it's accessible via 'zork' command"
    print_info "On macOS: brew install zork"
    print_info "On Ubuntu/Debian: apt-get install zork"
fi

# Check if Ollama is running
print_info "Checking Ollama connection..."
if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    print_status "Ollama is running"
else
    print_warning "Cannot connect to Ollama"
    print_info "Make sure Ollama is running with: ollama serve"
    print_info "And that you have the required model: ollama pull mistral-small3.1:latest"
fi

echo ""
print_info "Starting Zork LLM Player..."
echo ""

# Run the main Python script
python main.py

# Deactivate virtual environment
deactivate
print_status "Session completed"